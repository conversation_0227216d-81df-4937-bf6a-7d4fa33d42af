using System;
using FairyGUI;
using UnityEngine;

public class LobbyPanel : Panel
{
    public LobbyPanel()
    {
        packName = "Lobby";
        compName = "LobbyPanel";
    }

    private GTextField txtLevel;
    private CoinBar coinBar;
    private HeartBar heartBar;
    private BoxLevelBar levelBar;
    private GButton btnPig;

    protected override void DoInitialize()
    {
        txtLevel = contentPane.GetChild("txtLevel").asTextField;
        txtLevel.text = $"{LangUtil.GetText("txtLevelPrefix")} {GameGlobal.Level}"; // 关卡 

        coinBar = new CoinBar(contentPane.GetChild("coinBar"));
        heartBar = new HeartBar(contentPane.GetChild("heartBar"));
        // starBar = new BoxStarBar(contentPane.GetChild("starBar"));
        levelBar = new BoxLevelBar(contentPane.GetChild("levelBar"), contentPane);
        btnPig = contentPane.GetChild("btnPig").asButton;
        var txtVersion = contentPane.GetChild("txtVersion").asTextField;
        txtVersion.text = $"ver {GameConfig.GetVer()}.{AssetBundleManager.GetVersion2()}";
        FlSdk.Inst.ReportCoinAndHeart();
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnStart":
                if (HeartCooldown.Instance.GetCurrentHearts() > 0)
                {
                    new CmdEnterBattle().Execute();
                    Hide();
                }
                else
                {
                    ShowBuyHeartPanel();
                }
                break;
            case "coinBar":
                // Create<ShopPanel>();
                break;
            case "heartBar":
                if (HeartCooldown.Instance.IsFull)
                {
                    TipMgr.ShowTip(LangUtil.GetText("txtHeartFull")); // 体力已满！
                }
                else
                {
                    ShowBuyHeartPanel();
                }
                break;
            case "btnPig":
                Create((PigPanel panel) =>
                {
                    panel.OnGetSuccess = (gold) =>
                    {
                        UIEffectUtil.FlyItem(contentPane, btnPig.xy, ItemId.ItemGold, gold);
                    };
                });
                break;
            case "btnShop":
                Create<ShopPanel>();
                break;
            case "btnSetting":
                Create((SettingPanel panel) => panel.SetData(SettingPanel.SettingViewMode.Setting));
                break;
        }
    }

    private void ShowBuyHeartPanel()
    {
        Create((BuyHeartPanel panel) =>
        {
            panel.OnBuySuccess = (buyCount) =>
            {
                var startPos = new Vector2(GRoot.inst.width / 2f, GRoot.inst.height / 2f);
                var targetPos = heartBar.GetHeartPos();
                for (int i = 0; i < buyCount; i++)
                {
                    var heart = UIPackage.CreateObject(packName, "Heart");
                    var bornPos = startPos;
                    if (buyCount > 1)
                    {
                        bornPos += UnityEngine.Random.insideUnitCircle * UnityEngine.Random.Range(-150, 150);
                    }
                    heart.xy = startPos;
                    contentPane.AddChild(heart);
                    heart.TweenMove(bornPos, 0.2f).OnComplete(() =>
                    {
                        if (contentPane.isDisposed) return;
                        heart.TweenScale(Vector2.one * 0.4f, 0.5f);
                        heart.TweenMove(targetPos, 0.5f).SetDelay(UnityEngine.Random.Range(0, 0.2f)).SetEase(EaseType.BackIn).OnComplete(() =>
                        {
                            if (contentPane.isDisposed) return;
                            heart.Dispose();
                        });
                    });
                }
            };
        });
    }
}