# 设置配置规范 (Setting Configuration Rules)

## 概述
本文档定义了游戏设置配置系统的规范，包括JSON配置文件格式、代码结构和新增设置项的标准流程。

## 1. JSON配置文件格式

### 文件位置
- 配置文件：`Excels/json/Setting.json`

### JSON结构
```json
[
  {
    "id": "配置项唯一标识",
    "desc": "配置项中文描述",
    "paramInt": 整数值,
    "paramString": "字符串值"
  }
]
```

### 字段说明
- **id**: 配置项的唯一标识符，使用驼峰命名法
- **desc**: 配置项的中文描述，用于说明配置项的作用
- **paramInt**: 整数类型的配置值
- **paramString**: 字符串类型的配置值

### 注意事项
- 每个配置项只能使用 `paramInt` 或 `paramString` 其中一个
- `id` 必须唯一，不能重复
- 字符串中的中文逗号会自动转换为英文逗号

## 2. 代码结构

### InfoSetting类
```csharp
public class InfoSetting : ConfigInfoBase
{
    public string id;           // 配置项ID
    public int paramInt;        // 整数参数
    public string paramString;  // 字符串参数
    
    public override object GetKey(int index)
    {
        return id;
    }

    public override void Parse(JsonData json)
    {
        id = JsonUtil.ToString(json, "id");
        paramInt = JsonUtil.ToInt(json, "paramInt");
        paramString = JsonUtil.ToString(json, "paramString");
        
        // 自动转换中文逗号为英文逗号
        if (!string.IsNullOrEmpty(paramString))
        {
            paramString = paramString.Replace('，', ',');
        }
    }
}
```

### ConfigSetting类结构
```csharp
public class ConfigSetting : ConfigBase
{
    // 单例设置对象
    private static SettingWrapper _setting;
    public static SettingWrapper Setting
    {
        get
        {
            _setting ??= new SettingWrapper();
            return _setting;
        }
    }

    // 设置包装类
    public class SettingWrapper
    {
        /// <summary>配置项中文描述</summary>
        public int/string/float[] 字段名;
        
        // 特殊方法
        public int GetItemCanUseMax(int level) => ConfigSetting.GetItemCanUseMax(level);
    }

    // 配置数据缓存方法
    internal override void CacheData(object key, ConfigInfoBase info)
    {
        var infoSetting = info as InfoSetting;
        var id = key.ToString();
        
        switch (id)
        {
            case "配置项ID":
                Setting.字段名 = infoSetting.paramInt/paramString;
                break;
            // 特殊处理的配置项
            case "goldMult":
                var goldMult = infoSetting.paramString;
                var mults = goldMult.Split(',');
                Setting.goldMults = new float[mults.Length];
                for (int i = 0; i < mults.Length; i++)
                {
                    float.TryParse(mults[i], out Setting.goldMults[i]);
                }
                break;
        }
    }
}
```

## 3. 新增设置项规范

### 步骤1：添加JSON配置
在 `Excels/json/Setting.json` 中添加新的配置项：
```json
{
  "id": "newSettingId",
  "desc": "新设置项的中文描述",
  "paramInt": 默认值
}
```

### 步骤2：添加字段到SettingWrapper
在 `ConfigSetting.SettingWrapper` 类中添加对应字段：
```csharp
/// <summary>新设置项的中文描述</summary>
public int newSettingId;
```

### 步骤3：添加CacheData处理
在 `ConfigSetting.CacheData` 方法的switch语句中添加：
```csharp
case "newSettingId":
    Setting.newSettingId = infoSetting.paramInt;
    break;
```

### 步骤4：使用配置
在代码中使用新配置：
```csharp
int value = ConfigSetting.Setting.newSettingId;
```

## 4. 特殊配置类型处理

### 数组类型配置
对于需要解析为数组的字符串配置（如goldMult）：
```csharp
case "arrayConfig":
    var arrayStr = infoSetting.paramString;
    var parts = arrayStr.Split(',');
    Setting.arrayConfig = new float[parts.Length];
    for (int i = 0; i < parts.Length; i++)
    {
        float.TryParse(parts[i], out Setting.arrayConfig[i]);
    }
    break;
```

### 复杂字符串配置
对于需要特殊解析的字符串配置（如itemCanUseMax）：
```csharp
case "complexConfig":
    Setting.complexConfigStr = infoSetting.paramString;
    break;
```

## 5. 命名规范

### JSON中的id命名
- 使用驼峰命名法
- 首字母小写
- 描述性强，能清楚表达配置项用途
- 例：`everyLevelIncreaseDifficulty`、`startHuaCount`

### C#字段命名
- 与JSON中的id保持一致
- 使用驼峰命名法，首字母小写
- 例：`everyLevelIncreaseDifficulty`、`startHuaCount`

### 注释规范
- 使用XML文档注释 `/// <summary>描述</summary>`
- 注释内容与JSON中的desc保持一致
- 简洁明了，说明配置项的作用

## 6. 注意事项

### 类型安全
- 整数配置使用 `paramInt`
- 字符串配置使用 `paramString`
- 数组配置使用字符串存储，在CacheData中解析

## 7. 示例

### 完整的新增配置示例
假设要添加一个"每日免费抽奖次数"的配置：

**JSON配置：**
```json
{
  "id": "dailyFreeLotteryCount",
  "desc": "每日免费抽奖次数",
  "paramInt": 3
}
```

**SettingWrapper字段：**
```csharp
/// <summary>每日免费抽奖次数</summary>
public int dailyFreeLotteryCount;
```

**CacheData处理：**
```csharp
case "dailyFreeLotteryCount":
    Setting.dailyFreeLotteryCount = infoSetting.paramInt;
    break;
```

**使用方式：**
```csharp
int freeCount = ConfigSetting.Setting.dailyFreeLotteryCount;
```