
#if WXGAME
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using LitJson;
using UnityEngine;
using UnityEngine.Networking;
using WeChatWASM;
public class PlatformWxgame : PlatformBase
{
    //微信js调试
    //  const gl = GameGlobal.canvas.getContext('webgl')
    //  gl.clear(gl.COLOR_BUFFER_BIT);
    private static readonly string BaseUrl = "";
    public class Scope
    {
        public static string WxFriendInteraction = "scope.WxFriendInteraction";
        public static string UserInfo = "scope.userInfo";
    }


    string[] shareTitles = new string[]
    {
        "能在5分钟之内通过算你厉害！",
        "点击即玩，休闲解压!"
    };
    string[] imageUrls = new string[]
    {
        "https://mmocgame.qpic.cn/wechatgame/jPwa0VeKr2djKSyIv6qYpOh01BNTOZOGLWbUqBc2qAuTNT1NPmvQNxx1MtPq7Rjt/0"
    };

    string[] imageUrlIds = new string[]
    {
        "skYza85nSbGTNdbERdIjTQ=="
    };

    private Action _shareCallback;
    private string _shareType;
    private bool _isFirstShare = true;
    private bool _shareMustSuccess = false;
    private float _startShareTime;
    protected override void OnInit(Action initCallback)
    {
        WeChatWASM.WX.InitSDK((res) =>
        {
            _OnInit();
            initCallback?.Invoke();
        });
    }
    
    private void _OnInit(){
        WX.ReportGameStart();
        ReportScene(1003);//平台初始化
        WX.SetDataCDN(GameConfig.CdnUrl);
        WX.OnLaunchProgress((res) =>
        {
            if (res.type == LaunchEventType.prepareGame)
            {
                FlSdk.Inst.ReportLoadTime(GameGlobal.IsNewPlayer, res.runTimeMs);
            }
        });

        // var randTitleIdx = UnityEngine.Random.Range(0, shareTitles.Length);
        // var randImageIdx = UnityEngine.Random.Range(0, imageUrls.Length);
        // WX.OnShareAppMessage(new WXShareAppMessageParam()
        // {
        //     title = shareTitles[randTitleIdx],
        //     imageUrl = imageUrls[randImageIdx],
        //     imageUrlId = imageUrlIds[randImageIdx]
        // });

        WX.UpdateShareMenu(new UpdateShareMenuOption()
        {
            withShareTicket = true
        });
        // WX.OnShow((res) =>
        // {
        //     if (_shareCallback != null)
        //     {
        //         float failPercent = 0.5f;
        //         var timeToShort = Time.realtimeSinceStartup - _startShareTime;
        //         if (timeToShort < 3)
        //         {
        //             if (_isFirstShare)
        //             {
        //                 //首次成功概率较小
        //                 failPercent = 0.8f;
        //                 _isFirstShare = false;
        //                 _shareMustSuccess = true;
        //             }
        //             else if (_shareMustSuccess)
        //             {
        //                 //第二次必定成功
        //                 failPercent = 0f;
        //                 _shareMustSuccess = false;
        //             }
        //             else
        //             {
        //                 failPercent = 0.8f;
        //             }
        //         }
        //         else
        //         {
        //             failPercent = 0.2f;
        //         }

        //         var isShareSuccess = UnityEngine.Random.value > failPercent;
        //         if (isShareSuccess)
        //         {
        //             ShareSuccess();
        //         }
        //         else
        //         {
        //             ShareFail();
        //         }
        //     }
        // });

        ReportScene(1004);//平台初始化完成
    }

    private void ShareSuccess()
    {
        _shareCallback?.Invoke();
        _shareCallback = null;
        Report(ReportType.Share, 1);
    }

    private void ShareFail()
    {
        WX.ShowModal(new ShowModalOption()
        {
            content = "分享失败，请分享到新的群",
            confirmText = "去分享",
            success = (res) =>
            {
                if (res.confirm)
                {
                    Share(_shareType, _shareCallback);
                }
            }
        });
    }


    private WXRewardedVideoAd RewardedVideoAd;
    public override void ShowVideoAd(string adId, Action OnAdReward, Action OnCancel = null, Action<int, string> OnFailed = null)
    {
        if (Session.jumpVideoAd)
        {
            OnAdReward?.Invoke();
            return;
        }
        
        FlSdk.Inst.ShowVideoAd(adId, OnAdReward, () =>
        {
            OnCancel?.Invoke();
            OnFailed?.Invoke(0, string.Empty);
        });

        return;
        if (RewardedVideoAd != null)
        {
            return;
        }

        WX.ShowLoading(new ShowLoadingOption() { title = "加载中", mask = true });

        Report(ReportType.AdVideo, adId, 0);
        RewardedVideoAd = WX.CreateRewardedVideoAd(new WXCreateRewardedVideoAdParam()
        {
            adUnitId = adId,
        });

        RewardedVideoAd.OnLoad((res) =>
        {
            Debug.Log("RewardedVideoAd.OnLoad");
        });
        RewardedVideoAd.OnError((err) =>
        {
            OnFailed?.Invoke(err.errCode, err.errMsg);
            RewardedVideoAd = null;
            Debug.Log("RewardedVideoAd.OnError");
        });
        RewardedVideoAd.OnClose((res) =>
        {
            if (res != null && res.isEnded)
            {
                Report(ReportType.AdVideo, adId, 1);
                OnAdReward?.Invoke();
            }
            else
            {
                Report(ReportType.AdVideo, adId, 2);
                OnCancel?.Invoke();
            }
            RewardedVideoAd = null;
            Debug.Log("RewardedVideoAd.OnClose");
        });

        RewardedVideoAd.Load(success: (res) =>
        {
            Debug.Log("RewardedVideoAd.Load success1");
            RewardedVideoAd.Show(success: (res) =>
            {
                WX.HideLoading(new HideLoadingOption());
            }, failed: (res) =>
            {
                Debug.Log("RewardedVideoAd.Load success2");
                OnFailed?.Invoke(res.errCode, res.errMsg);
                RewardedVideoAd = null;
                WX.HideLoading(new HideLoadingOption());
            });
        }, failed: (res) =>
        {
            Debug.Log("RewardedVideoAd.Load failed2:" + res.errMsg);
            OnFailed?.Invoke(res.errCode, res.errMsg);
            RewardedVideoAd = null;
            WX.HideLoading(new HideLoadingOption());
        });
    }

    public override void ReportScene<T>(T value)
    {
        // if (!GameGlobal.IsNewPlayer)
        //     return;

        // if (value is int)
        // {
        //     int sceneId = (int)(object)value;
        //     WX.ReportScene(new ReportSceneOption()
        //     {
        //         sceneId = sceneId
        //     });
        // }

        // Report(value.ToString(), 0);
    }

    private void Report<T>(string key, T value, int value2)
    {
        var reportUrl = BaseUrl + "/pointLog";
        reportUrl += "?userId=" + GameGlobal.ClientId;
        reportUrl += "&event_name=" + key;
        reportUrl += "&event_value=" + value.ToString();
        reportUrl += "&value=" + value2.ToString();
        reportUrl += "&openId=" + GameGlobal.OpenId;
        // URLRequest.Get(reportUrl);

        // Debug.Log("[Report] key:" + key + "  value:" + value.ToString());
    }

    public override void Report<T>(string key, T value)
    {
        // WX.ReportEvent(key, value);
        Report(key, value, 0);
    }


    public override void Share(string shareType, Action OnShare)
    {
        _shareCallback = OnShare;
        _shareType = shareType;
        FlSdk.Inst.Share(shareType, OnShare, () =>
        {
            // WX.ShowModal(new ShowModalOption()
            // {
            //     content = "分享失败，请分享到新的群",
            //     confirmText = "去分享",
            //     success = (res) =>
            //     {
            //         if (res.confirm)
            //         {
            //             Share(_shareType, _shareCallback);
            //         }
            //     }
            // });
        });
        return;
        _startShareTime = Time.realtimeSinceStartup;
        _shareCallback = OnShare;
        _shareType = shareType;
        var randTitleIdx = UnityEngine.Random.Range(0, shareTitles.Length);
        var randImageIdx = UnityEngine.Random.Range(0, imageUrls.Length);
        WX.ShareAppMessage(new ShareAppMessageOption()
        {
            title = shareTitles[randTitleIdx],
            imageUrl = imageUrls[randImageIdx],
            imageUrlId = imageUrlIds[randImageIdx]
        });
        // TimerManager.Instance.DelayCall(1f, OnShare);

        Report(ReportType.Share, 0);
    }

    public override void SetStorage<T>(string key, T value)
    {
        if (typeof(T) == typeof(string))
        {
            WX.StorageSetStringSync(key, Convert.ToString(value));
        }
        else if (typeof(T) == typeof(int))
        {
            WX.StorageSetIntSync(key, Convert.ToInt32(value));
        }
        else if (typeof(T) == typeof(float))
        {
            WX.StorageSetFloatSync(key, Convert.ToSingle(value));
        }
        else if (typeof(T) == typeof(bool))
        {
            WX.StorageSetIntSync(key, Convert.ToBoolean(value) ? 1 : 0);
        }
    }
    public override T GetStorage<T>(string key, object defaultValue)
    {
        if (typeof(T) == typeof(string))
        {
            return (T)Convert.ChangeType(WX.StorageGetStringSync(key, (string)defaultValue), typeof(T));
        }
        else if (typeof(T) == typeof(int))
        {
            return (T)Convert.ChangeType(WX.StorageGetIntSync(key, (int)defaultValue), typeof(T));
        }
        else if (typeof(T) == typeof(float))
        {
            return (T)Convert.ChangeType(WX.StorageGetFloatSync(key, (float)defaultValue), typeof(T));
        }
        return default;
    }


    protected override void OnVibrateShort(VibrateType type)
    {
        var typeStr = "light";
        if (type == VibrateType.medium) typeStr = "medium";
        else if (type == VibrateType.heavy) typeStr = "heavy";
        WeChatWASM.WX.VibrateShort(new WeChatWASM.VibrateShortOption() { type = typeStr });
    }

    private WXUserInfoButton userBtn;
    public void CreateUserInfoButton(float x, float y, float width, float height, Action<int> onTap)
    {
        if (userBtn != null)
        {
            userBtn.Show();
        }
        else
        {
            userBtn = WeChatWASM.WX.CreateUserInfoButton((int)x, (int)y, (int)width, (int)height, "zh_CN", true);
        }

        userBtn.OffTap();
        userBtn.OnTap((res) =>
        {
            WX.Login(new LoginOption()
            {
                success = (loginRes) =>
                {
                    var loginCode = loginRes.code;
                    UpdateRank("", onTap, loginCode, res.userInfo);
                }
            });
        });
    }

    public void UpdateRank(string openId, Action<int> onSuccess = null, string loginCode = "", WXUserInfo? userInfo = null)
    {
        var level = StorageMgr.Level;

        var info = BaseUrl + "/updateRank";
        info += "?gameId=1";
        info += "&openId=" + openId;
        info += "&level=" + level;

        if (!string.IsNullOrEmpty(loginCode))
        {
            info += "&code=" + UnityWebRequest.EscapeURL(loginCode);
        }

        if (userInfo.HasValue)
        {
            info += "&userInfo=" + UnityWebRequest.EscapeURL(JsonUtility.ToJson(userInfo));
        }
        // info += "&iv=" + UnityWebRequest.EscapeURL(iv);
        // info += "&encryptedData=" + UnityWebRequest.EscapeURL(encryptedData);


        // var url = BaseUrl + "/updateRank";
        // var data = new URLRequestData();
        // data.Add("gameId", "1");
        // data.Add("level", level);
        // if (!string.IsNullOrEmpty(openId))
        // {
        //     data.Add("openId", openId);
        // }
        // if (!string.IsNullOrEmpty(loginCode))
        // {
        //     data.Add("code", loginCode);
        //     // data.Add("iv", iv);
        //     // data.Add("encryptedData", encryptedData);
        // }
        // URLRequest.Post(url, data, (res) =>
        // {
        //     Debug.Log("UpdateRank res:" + res.GetString());
        // });


        Debug.Log("UpdateRank");
        URLRequest.Get(info, (res) =>
        {
            try
            {
                var data = JsonMapper.ToObject(res.GetString());
                var code = JsonUtil.ToInt(data, "code");
                var message = JsonUtil.ToString(data, "message");

                if (JsonUtil.ContainKey(data, "data"))
                {
                    var extData = JsonUtil.ToJson(data, "data");
                    var openId = JsonUtil.ToString(extData, "openId");
                    GameGlobal.OpenId = StorageMgr.OpenId = openId;
                }

                Debug.Log($"UpdateRank code:{code} message:{message}");
                onSuccess?.Invoke(code);
            }
            catch (System.Exception e)
            {
                Debug.LogWarning("[UpdateRank] error:" + e.Message);
            }
        });
    }

    public void GetRankList(Action<JsonData> onRespond)
    {
        try
        {
            var url = BaseUrl + "/ranklist";
            URLRequest.Get(url, (res) =>
            {
                Debug.Log("getRankList res");
                onRespond?.Invoke(JsonMapper.ToObject(res.GetString()));
            });
        }
        catch (System.Exception) { }
    }



    public void ShowUserInfoButton() { userBtn?.Show(); }
    public void HideUserInfoButton() { userBtn?.Hide(); }


    public Task<bool> RequestAuthorize(string scope)
    {
        var tsc = new TaskCompletionSource<bool>();
        GetSetting(scope,
            onSuccess: () =>
            {
                tsc.SetResult(true);
            },
            onFail: () =>
            {
                tsc.SetResult(false);
            },
            onUnAuth: () =>
            {
                WeChatWASM.WX.Authorize(new WeChatWASM.AuthorizeOption
                {
                    scope = scope,
                    fail = (failRes) =>
                    {
                        Debug.LogWarning("[PlatformWxgame] Authorize fail：" + failRes.errMsg);
                        tsc.SetResult(false);
                    },
                    success = (authorizeRes) =>
                    {
                        tsc.SetResult(true);
                    }
                });
            }
        );
        return tsc.Task;
    }

    private void GetSetting(string scope, Action onSuccess, Action onFail, Action onUnAuth)
    {
        WeChatWASM.WX.GetSetting(new WeChatWASM.GetSettingOption
        {
            fail = (failRes) =>
            {
                Debug.LogWarning("[PlatformWxgame] GetSetting fail：" + failRes.errMsg);
                onFail.Invoke();
            },
            success = (getSettingRes) =>
            {
                if (getSettingRes.authSetting.ContainsKey(scope))
                {
                    if (getSettingRes.authSetting[scope] == false)
                    {
                        WeChatWASM.WX.OpenSetting(new WeChatWASM.OpenSettingOption
                        {
                            fail = (failRes) =>
                            {
                                Debug.LogWarning("[PlatformWxgame] OpenSetting fail：" + failRes.errMsg);
                                onFail.Invoke();
                            },
                            success = (openRes) =>
                            {
                                if (openRes.authSetting[scope] == true)
                                {
                                    onSuccess.Invoke();
                                };
                            }
                        });
                    }
                    else
                    {
                        onSuccess.Invoke();
                    }
                }
                else
                {
                    onUnAuth.Invoke();
                }
            }
        });
    }

    public void ReportScore(int score)
    {
        var msgData = new OpenDataMessage
        {
            type = "setUserRecord",
            score = score
        };
        string msg = JsonUtility.ToJson(msgData);
        WeChatWASM.WX.GetOpenDataContext().PostMessage(msg);
    }

    public void ShowFriendsRank()
    {
        var msgData = new OpenDataMessage
        {
            type = "showFriendsRank",

        };

        string msg = JsonUtility.ToJson(msgData);
        WeChatWASM.WX.GetOpenDataContext().PostMessage(msg);
    }


    private class OpenDataMessage
    {
        public string type;

        public string shareTicket;

        public int score;
    }
}
#endif