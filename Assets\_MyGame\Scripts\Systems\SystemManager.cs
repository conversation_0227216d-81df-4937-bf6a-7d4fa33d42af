using System;
using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// 系统管理器，负责管理所有系统的生命周期
/// </summary>
public class SystemManager : MonoBehaviour
{
    private static SystemManager instance;
    public static SystemManager Inst
    {
        get
        {
            if (instance == null)
            {
                GameObject go = new GameObject("SystemManager");
                instance = go.AddComponent<SystemManager>();
                DontDestroyOnLoad(go);
            }
            return instance;
        }
    }


    private readonly Dictionary<Type, GlobalSystem> globalSystems = new();
    private readonly Dictionary<Type, GameplaySystem> gameplaySystems = new();

#if UNITY_EDITOR
    [SerializeField, HideInInspector]
    private List<string> debugGlobalSystemNames = new();
    [SerializeField, HideInInspector]
    private List<string> debugGameplaySystemNames = new();
#endif

    private void Update()
    {
        foreach (var system in globalSystems.Values)
        {
            system.Update();
        }

        foreach (var system in gameplaySystems.Values)
        {
            system.Update();
        }

#if UNITY_EDITOR
        UpdateDebugInfo();
#endif
    }

    /// <summary>
    /// 获取全局系统，如果不存在则创建
    /// </summary>
    public T GetGlobalSystem<T>() where T : GlobalSystem, new()
    {
        Type systemType = typeof(T);
        if (!globalSystems.ContainsKey(systemType))
        {
            T newSystem = new T();
            globalSystems.Add(systemType, newSystem);
            newSystem.Init();
        }
        return (T)globalSystems[systemType];
    }

    /// <summary>
    /// 获取玩法系统，如果不存在则创建
    /// </summary>
    public T GetGameplaySystem<T>() where T : GameplaySystem, new()
    {
        Type systemType = typeof(T);
        if (!gameplaySystems.ContainsKey(systemType))
        {
            T newSystem = new T();
            gameplaySystems.Add(systemType, newSystem);
            newSystem.Init();
        }
        return (T)gameplaySystems[systemType];
    }

    /// <summary>
    /// 移除指定的全局系统
    /// </summary>
    public void RemoveGlobalSystem<T>() where T : GlobalSystem
    {
        Type systemType = typeof(T);
        if (globalSystems.ContainsKey(systemType))
        {
            globalSystems[systemType].Dispose();
            globalSystems.Remove(systemType);
        }
    }

    /// <summary>
    /// 移除指定的玩法系统
    /// </summary>
    public void RemoveGameplaySystem<T>() where T : GameplaySystem
    {
        Type systemType = typeof(T);
        if (gameplaySystems.ContainsKey(systemType))
        {
            gameplaySystems[systemType].Dispose();
            gameplaySystems.Remove(systemType);
        }
    }

    /// <summary>
    /// 清理全局系统
    /// </summary>
    public void ClearGlobalSystems()
    {
        foreach (var system in globalSystems.Values)
        {
            system.Dispose();
        }
        globalSystems.Clear();
    }

    /// <summary>
    /// 清理所有玩法系统
    /// </summary>
    public void ClearGameplaySystems()
    {
        foreach (var system in gameplaySystems.Values)
        {
            system.Dispose();
        }
        gameplaySystems.Clear();
    }

#if UNITY_EDITOR
    private void UpdateDebugInfo()
    {
        debugGlobalSystemNames.Clear();
        debugGameplaySystemNames.Clear();

        foreach (var system in globalSystems)
        {
            var info = $"{system.Key.Name}";
            var debugInfo = system.Value?.GetDebugInfo();
            if (!string.IsNullOrEmpty(debugInfo))
            {
                info += $" [{debugInfo}]";
            }
            debugGlobalSystemNames.Add(info);
        }

        foreach (var system in gameplaySystems)
        {
            var info = $"{system.Key.Name}";
            var debugInfo = system.Value?.GetDebugInfo();
            if (!string.IsNullOrEmpty(debugInfo))
            {
                info += $" [{debugInfo}]";
            }
            debugGameplaySystemNames.Add(info);
        }

        EditorUtility.SetDirty(this);
    }

    private void OnValidate()
    {
        UpdateDebugInfo();
    }
#endif
}
