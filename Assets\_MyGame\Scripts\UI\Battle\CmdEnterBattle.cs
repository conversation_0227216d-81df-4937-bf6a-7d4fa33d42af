public class CmdEnterBattle
{
    public bool Execute(int level = 0, bool isFree = false, bool needReportChapterEnd = false)
    {
        if (level != 0)
        {
            GameGlobal.EnterLevel = level;
        }
        else
        {
            GameGlobal.EnterLevel = GameGlobal.Level;
        }

        if (isFree)
        {
            GameRoot.SwitchHandler<BattleHandler>();
            return true;
        }
        else
        {
            //需要消耗体力
            if (HeartCooldown.Instance.GetCurrentHearts() > 0)
            {
                HeartCooldown.Instance.ConsumeHeart();
                if (needReportChapterEnd)
                {
                    FlSdk.Inst.ReportChapterEnd(false);
                }
                GameRoot.SwitchHandler<BattleHandler>();
                return true;
            }
            else
            {
                Panel.Create<GetHeartPanel>();
            }
        }
        return false;
    }
}