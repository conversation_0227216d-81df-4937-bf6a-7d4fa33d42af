using System;
using System.Collections.Generic;
using FairyGUI;

public class ShopPanel : Panel
{
    public ShopPanel()
    {
        packName = "Lobby";
        compName = "ShopPanel";
        modal = true;
    }

    private GList listItem;
    private List<InfoShopItem> shopItems;
    protected override void DoInitialize()
    {
        listItem = contentPane.GetChild("listItem").asList;
        listItem.itemRenderer = UpdateItem;

        shopItems = ConfigShopItem.GetDatas();
        listItem.numItems = shopItems.Count;
    }

    private void UpdateItem(int index, GObject item)
    {
        var txtName = item.asCom.GetChild("txtName").asTextField;
        var icon = item.asCom.GetChild("icon").asLoader;
        var txtCount = item.asCom.GetChild("txtCount").asTextField;
        var btnBuy = item.asCom.GetChild("btnBuy").asButton;
        var btnFree = item.asCom.GetChild("btnFree").asButton;

        var infoShopItem = shopItems[index];
        var infoItem = ConfigItem.GetData(infoShopItem.itemId);
        txtName.text = infoItem.name;
        icon.url = infoItem.IconUrl;
        txtCount.text = "x " + infoShopItem.count.ToString();
        btnBuy.text = infoShopItem.buyGold.ToString();
        btnBuy.visible = infoShopItem.buyGold != 0;

        btnBuy.onClick.Set(() =>
        {
            if (GameGlobal.ConsumeGold(infoShopItem.buyGold))
            {
                GameGlobal.IncrementItemCount(infoShopItem.itemId, infoShopItem.count);
                TipMgr.ShowTip(LangUtil.GetText("txtBuySuccess")); // 购买成功
            }
        });
        btnFree.onClick.Set(() =>
        {
            Platform.Instance.ShowVideoAd(AdType.buyItemBulb, () =>
            {
                GameGlobal.IncrementItemCount(infoShopItem.itemId, infoShopItem.count);
                TipMgr.ShowTip(LangUtil.GetText("txtBuySuccess")); // 购买成功
            });
        });
    }


    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                Hide();
                break;
        }
    }
}