public class GetHeartPanel : Panel
{
    public GetHeartPanel()
    {
        packName = "Battle";
        compName = "GetHeartPanel";
        modal = true;
    }

    protected override void DoInitialize()
    {
        FlSdk.Inst.ReportVideoExposure(1);
    }
    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                Hide();
                break;
            case "btnFree":
                Platform.Instance.ShowVideoAd(AdType.freeHeart, () =>
                {
                    // GameGlobal.SwitchShareOrVideo();
                    FlSdk.Inst.ReportBuyStamina(ConfigSetting.Setting.freeHeartCount, true, HeartCooldown.Instance.GetCurrentHearts());
                    HeartCooldown.Instance.RecoverHeart(ConfigSetting.Setting.freeHeartCount);
                    Hide();
                    TipMgr.ShowTip(LangUtil.GetText("txtGetHeart", ConfigSetting.Setting.freeHeartCount)); // 获得{0}点体力
                });
                break;
        }
    }
}