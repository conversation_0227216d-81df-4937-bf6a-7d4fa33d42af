using System;
using FairyGUI;

public class SettingPanel : Panel
{
    public static int CloseType_Restart = 1;
    public static int CloseType_Exit = 2;
    public enum SettingViewMode
    {
        Pause,
        Setting
    }

    public SettingPanel()
    {
        packName = "Battle";
        compName = "SettingPanel";
        modal = true;
    }

    private GButton btnSound;
    private GButton btnVibration;
    private Controller c1;
    private GSlider sliderBgm;
    private GSlider sliderSfx;

    protected override void DoInitialize()
    {
        c1 = contentPane.GetController("c1");

        // btnSound = contentPane.GetChild("btnSound").asButton;
        // btnSound.selected = StorageMgr.SoundEffectOn;
        // btnSound.onChanged.Add(OnSoundChanged);
        sliderBgm = contentPane.GetChild("sliderBgm").asSlider;
        sliderBgm.max = 1;
        sliderBgm.value = GameGlobal.VolumeBgm;
        sliderBgm.onChanged.Set(() =>
        {
            GameGlobal.VolumeBgm = StorageMgr.VolumeBgm = (float)sliderBgm.value;
            SoundManager.SetBgmVolume(GameGlobal.VolumeBgm);
        });

        sliderSfx = contentPane.GetChild("sliderSfx").asSlider;
        sliderSfx.max = 1;
        sliderSfx.value = GameGlobal.VolumeEffect;
        sliderSfx.onChanged.Set(() =>
        {
            GameGlobal.VolumeEffect = StorageMgr.VolumeEffect = (float)sliderSfx.value;
            SoundManager.SetEffectVolume(GameGlobal.VolumeEffect);
        });

        btnVibration = contentPane.GetChild("btnVibration").asButton;
        btnVibration.selected = StorageMgr.VibrationOn;
        btnVibration.onChanged.Add(OnVibrationChanged);
    }

    public void SetData(SettingViewMode viewMode)
    {
        // var boxPause = contentPane.GetChild("boxPause");
        // boxPause.visible = viewMode == SettingViewMode.Pause;


        var boxContent = contentPane.GetChild("boxContent").asGroup;
        if (viewMode == SettingViewMode.Setting)
        {
            c1.selectedIndex = 0;
            boxContent.height = 530;
        }
        else
        {
            c1.selectedIndex = 1;
            boxContent.height = 860;
        }


    }

    private void OnSoundChanged(EventContext context)
    {
        StorageMgr.SoundEffectOn = btnSound.selected;
        SoundManager.SetMute(!btnSound.selected);
    }

    private void OnVibrationChanged(EventContext context)
    {
        GameGlobal.VibrationOn = StorageMgr.VibrationOn = btnVibration.selected;
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                this.Hide();
                break;
            case "btnRestart":
                // #if UNITY_EDITOR
                //                 Hide();
                //                 GameGlobal.IncrementLevel();
                //                 new CmdEnterBattle().Execute(true);
                //                 return;
                // #endif
                // Platform.Instance.ShowVideoAd(AdType.restart, () =>
                // {
                //     this.Hide(CloseType_Restart);
                //     new CmdEnterBattle().Execute(true);
                // });

                this.Hide(CloseType_Restart);
                new CmdEnterBattle().Execute(isFree: true);
                break;
            case "btnHome":
                this.Hide(CloseType_Exit);
                Create((ExitPanel panel) =>
                {
                    panel.SetData(FailReason.None);
                    panel.OnClosed = (type) =>
                    {
                        NotifyMgr.Event(NotifyNames.ResumeGame);
                    };
                });
                break;
        }
    }
}