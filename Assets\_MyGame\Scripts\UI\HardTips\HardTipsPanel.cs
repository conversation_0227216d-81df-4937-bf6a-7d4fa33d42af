public class HardTipsPanel : Panel
{
    public HardTipsPanel()
    {
        packName = "HardTips";
        compName = "HardTips";
        modal = true;
    }

    public void SetData(InfoGate infoGate)
    {
        var lblTips = contentPane.GetChild("lblTips").asTextField;
        lblTips.text = LangUtil.GetText("txtHardTipsEnter", infoGate.percentDesc); // 通关后将击败全国{ 0}的麻友
    }

    protected override void DoInitialize()
    {
        var t0 = contentPane.GetTransition("t0");
        t0.Play(() =>
        {
            Hide();
        });
    }
}