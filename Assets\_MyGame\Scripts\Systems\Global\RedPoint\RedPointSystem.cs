using System;
using System.Collections.Generic;

public class RedPointSystem : GlobalSystem
{
    private Dictionary<string, bool> redPointStates;
    public event Action<string> OnRedPointChanged;
    public event Action OnNeedRefresh;

    public void TriggerRefresh()
    {
        OnNeedRefresh?.Invoke();
    }

    protected override void OnInit()
    {
        redPointStates = new Dictionary<string, bool>();
    }

    public void SetRedPoint(string key, bool value)
    {
        if (!redPointStates.ContainsKey(key))
        {
            redPointStates[key] = value;
        }
        else if (redPointStates[key] != value)
        {
            redPointStates[key] = value;
        }
        OnRedPointChanged?.Invoke(key);
    }

    public bool HasRedPoint(string key)
    {
        return redPointStates.ContainsKey(key) && redPointStates[key];
    }

    public override void Dispose()
    {
        redPointStates.Clear();
        OnRedPointChanged = null;
        OnNeedRefresh = null;
    }

    public override string GetDebugInfo()
    {
        return $"RedPoint Count: {redPointStates.Count}";
    }
}