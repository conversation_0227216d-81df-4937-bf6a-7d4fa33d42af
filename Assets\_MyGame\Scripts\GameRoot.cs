﻿using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using FairyGUI;
using UnityEngine;
#if YOOASSET
using YooAsset;
#endif

internal class GameRoot : MonoBehaviour
{
#if YOOASSET
    public EPlayMode playMode = EPlayMode.EditorSimulateMode;
#endif

    public static GameRoot Instance { get; private set; }
    private static HandlerMgr handlerMgr = new HandlerMgr();
    public static void SwitchHandler<T>() where T : HandlerBase
    {
        handlerMgr.SwitchHandler(typeof(T));
    }
    public static T GetHandler<T>()
    {
        return (T)handlerMgr.GetHandler(typeof(T));
    }
    public static bool IsActiveHandler<T>()
    {
        return handlerMgr.IsActiveHandler(typeof(T));
    }

    private void Awake()
    {
        Application.targetFrameRate = 60;
        Instance = this;
        GameObject.DontDestroyOnLoad(this);
        Screen.sleepTimeout = SleepTimeout.NeverSleep;
        // FlSdk.ActiveReport();
    }

    private void Start()
    {
        LoadingPanel.Show();
#if UNITY_EDITOR
        OnFlSdkInitAction(0);
#else
        FlSdk.Inst.OnFlSdkInitAction = OnFlSdkInitAction;
        FlSdk.Inst.Init();
#endif
    }

    // private IEnumerator Start()
    private void OnFlSdkInitAction(int abTest)
    {
        LoadingPanel.SetProgress(1);
        InitPlatform();
        InitLocalConfig();
        InitAbTest(abTest);
        Platform.Instance.Init(InitGame);
        Platform.Instance.ReportScene(ReportType.Start);
    }

    private void InitGame()
    {
        StartCoroutine(OnInitGame());
    }

    private IEnumerator OnInitGame()
    {
        Platform.Instance.ReportScene(ReportType.InitGame);

#if YOOASSET
        var driver = new YooAssetsInitialize();
#if UNITY_WEBGL && !UNITY_EDITOR
        playMode = EPlayMode.WebPlayMode;
        YooAssets.SetCacheSystemDisableCacheOnWebGL();
#endif
        yield return StartCoroutine(driver.Initialize(playMode, Main.CdnDir, Main.AppendTimeTicks));
        AssetBundleManager.Initialize(Main.BundleDir, driver.package);
#endif
        AssetBundleManager.Initialize("Bundles/");
        handlerMgr.Register(new LobbyHandler());
        handlerMgr.Register(new BattleHandler());
        handlerMgr.Register(new EmptyHandler());

        UIConfig.modalLayerColor = new Color(0, 0, 0, 0.7f);
        UIConfig.bringWindowToFrontOnClick = false;
        DOTween.SetTweensCapacity(400, 50);
        InitFont();
        LoadConfig();
        GameGlobal.UnNewPlayer();
    }
    private void InitPlatform()
    {
        Platform.Set<PlatformBase>();

#if DYGAME && !UNITY_EDITOR
        Platform.Set<PlatformDouyin>();
#endif

#if WXGAME && !UNITY_EDITOR
        Platform.Set<PlatformWxgame>();
#endif
    }

    private void InitAbTest(int abTest)
    {
        Debug.Log("abTest :" + abTest);
        var channel = GameGlobal.Channel = StorageMgr.Channel;
        if (GameGlobal.IsNewPlayer && string.IsNullOrEmpty(channel))
        {
            GameGlobal.Channel = abTest switch
            {
                0 => "A",
                1 => "B",
                2 => "C",
                _ => "A",
            };
        }

        if (string.IsNullOrEmpty(GameGlobal.Channel))
            GameGlobal.Channel = "A";

        FlSdk.Inst.ReportAbTest(GameGlobal.Channel);
        Debug.Log("ab_test:" + GameGlobal.Channel);

        // try
        // {
        //     WeChatWASM.LaunchOptionsGame options = WeChatWASM.WX.GetLaunchOptionsSync();
        //     var query = options.query;
        //     // Debug.Log("options.query:" + JsonUtility.ToJson(query));
        //     if (query != null && query.TryGetValue("ab_test", out string ab_test))
        //     {
        //         Debug.Log("ab_test:" + ab_test);
        //         FlSdk.Inst.ReportAbTest(ab_test);
        //         GameGlobal.Channel = ab_test;
        //     }
        //     else
        //     {
        //         Debug.Log("ab_test:A");
        //         FlSdk.Inst.ReportAbTest("A");
        //     }
        // }
        // catch (System.Exception e)
        // {
        //     Debug.LogWarning("InitAbTest error:" + e.Message);
        // }
    }

    private void InitLocalConfig()
    {
        GameGlobal.Read();
        SoundManager.SetBgmVolume(GameGlobal.VolumeBgm);
        SoundManager.SetEffectVolume(GameGlobal.VolumeEffect);
        // SoundManager.SetMute(!StorageMgr.SoundEffectOn);

        // Debug.Log("GameGlobal.clientId:" + GameGlobal.ClientId);

    }

    private void LoadConfig()
    {
        ConfigLoader.Inst.LoadConfigs(InitCommonUI);
    }

    private void InitCommonUI()
    {
        FUILoader.LoadPackages(new string[] { "Common" }, () =>
        {
            StartGame();
        });

        GRoot.inst.onTouchBegin.Add(OnGolbalTouchBegin);
    }

    private void OnGolbalTouchBegin(EventContext context)
    {
        if (GRoot.inst.touchTarget is GButton)
        {
            SoundManager.PlayEffect("button");
        }
    }

    private void InitFont()
    {
        AssetBundleManager.LoadFont("Fonts/ALBBPHT-Black", (fontRes) =>
        {
            DynamicFont font = new DynamicFont();
            font.name = "ALBBPHT-Black";
            font.nativeFont = fontRes;
            FontManager.RegisterFont(font);

            UIConfig.defaultFont = "ALBBPHT-Black";
        });

#if UNITY_WEBGL && !UNITY_EDITOR
        // try
        // {
        //     // var fallbackUrl = GameConfig.CdnUrl + "/StreamingAssets/yoo/DefaultPackage/ALBBPHT-Black.ttf";
        //     var fallbackUrl = "https://cdn.res.jslhgame.com/fonts/simhei.ttf";
        //     WeChatWASM.WX.GetWXFont(fallbackUrl, (fontRes) =>
        //     {
        //         if (fontRes != null)
        //         {
        //             DynamicFont font = new DynamicFont();
        //             font.name = "wxfont";
        //             font.nativeFont = fontRes;
        //             FontManager.RegisterFont(font);

        //             // UIConfig.defaultFont = "wxfont";
        //         }
        //     });
        // }
        // catch (System.Exception e)
        // {
        //     Debug.LogWarning("[wxFont] " + e.Message);
        // }

#endif



        // AssetBundleManager.LoadFont("Fonts/AlibabaPuHuiTi-2-95-ExtraBold", (fontRes) =>
        // {
        //     DynamicFont font = new DynamicFont();
        //     font.name = "AlibabaPuHuiTi-2-95-ExtraBold";
        //     font.nativeFont = fontRes;
        //     FontManager.RegisterFont(font);

        // });

    }

    private void StartGame()
    {
        if (GameGlobal.NeedGuide)
        {
            new CmdEnterBattle().Execute(isFree: true);
        }
        else
        {
            handlerMgr.SwitchHandler<LobbyHandler>();
        }
    }
    private void Update()
    {
        handlerMgr.Update();
    }
}
