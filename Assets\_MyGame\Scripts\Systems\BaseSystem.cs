using UnityEngine;

/// <summary>
/// 系统基类，所有具体系统都需要继承此类
/// </summary>
public abstract class BaseSystem
{
    protected bool isInitialized;

    public virtual void Init()
    {
        if (isInitialized)
            return;
            
        OnInit();
        isInitialized = true;
    }

    public virtual void Update()
    {
        if (!isInitialized)
            return;
            
        OnUpdate();
    }

    protected virtual void OnInit() { }
    protected virtual void OnUpdate() { }
    public virtual void Dispose() { }
    public virtual string GetDebugInfo()
    {
        return string.Empty;
    }
}

/// <summary>
/// 全局系统基类，整个游戏生命周期内存在的系统
/// </summary>
public abstract class GlobalSystem : BaseSystem { }

/// <summary>
/// 玩法系统基类，仅在具体玩法场景中存在的系统
/// </summary>
public abstract class GameplaySystem : BaseSystem { }
