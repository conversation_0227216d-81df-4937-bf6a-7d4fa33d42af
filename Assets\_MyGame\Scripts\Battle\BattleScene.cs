﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using FairyGUI;
using UnityEngine;
using UnityEngine.Rendering.Universal;

public class BattleScene : MonoBehaviour
{
    public static BattleScene Inst { get; private set; }
    public Camera mainCamera;
    public LayerMask clickLayerMask;
    public CubeArea itemArea;
    public CubeArea bornTopArea;
    public CubeArea bornArea;
    public SlotBar slotBar;
    public GameObject coverCube;
    public GameObject shuffleCube;
    public Transform[] guideTrans;
    public int itemMajiangMaxCount = 2;
    public Vector2 createItemMajiangInterval = new Vector2(3 * 60 - 15f, 3 * 60 + 15f);
    private float _leftCreateItemMajiangTime = 60;
    private int _itemMajiangCount = 0;
    [NonSerialized] public Vector3 bornAreaPos;
    public Dictionary<int, MaJiang> majiangDic = new Dictionary<int, MaJiang>();
    private int typeCount;
    private Light mainLight;
    private BattleHandler battleHandler;
    private void Awake()
    {
        Inst = this;
        if (Camera.main != null)
        {
            mainCamera.GetUniversalAdditionalCameraData().renderType = CameraRenderType.Overlay;
            var addCameraData = Camera.main.GetUniversalAdditionalCameraData();
            if (!addCameraData.cameraStack.Contains(mainCamera))
            {
                addCameraData.cameraStack.Insert(0, mainCamera);
            }
        }
        mainLight = FindObjectOfType<Light>();
        bornAreaPos = bornArea.transform.position;
        shuffleCube.SetActive(false);
        ChangeLightRot(false);
    }

    public void ChangeLightRot(bool isResult)
    {
        mainLight.transform.rotation = isResult ? Quaternion.Euler(50, 0, 0) : Quaternion.Euler(105, 0, 0);
    }

    public void StartGuide(int typeCount, int[] nums)
    {
        if (nums.Length != 9)
        {
            Debug.LogError("nums.Length != 9");
            return;
        }
        OnGameStart();
        majiangDic.Clear();
        this.typeCount = typeCount;
        List<MaJiang> majiangs = new List<MaJiang>();
        for (int i = 0; i < guideTrans.Length; i++)
        {
            var tran = guideTrans[i];
            var maJiang = PoolMgr.Inst.Get<MaJiang>("MaJiang");
            maJiang.transform.SetPositionAndRotation(tran.position, tran.rotation);
            maJiang.SetInfo(nums[i]);
            majiangs.Add(maJiang);
            majiangDic.Add(maJiang.GetHashCode(), maJiang);
        }
        NotifyMgr.Event(NotifyNames.UpdateMajiangCount);
        GuideManager.Instance.EnqueueStep(GuideSetting.GetNewPlayerGuide(majiangs), (step) =>
        {
            FlSdk.Inst.ReportGuideComplete(step);
        });
    }

    private void OnGameStart()
    {
        isFirstOpenSlot = false;
        GameGlobal.ResetLevelStar();
        GameGlobal.ResetComboCount();
    }

    private void EnableCoverCube()
    {
        coverCube.SetActive(true);
        DOVirtual.DelayedCall(2f, () =>
        {
            if (this == null)
                return;
            coverCube.SetActive(false);
        });
    }

    public void StartGame(BattleHandler battleHandler, int typeCount, int topCount, List<int> nums, int lockSlotCount)
    {
        this.battleHandler = battleHandler;
        this.typeCount = typeCount;
        _leftCreateItemMajiangTime = UnityEngine.Random.Range(createItemMajiangInterval.x, createItemMajiangInterval.y);
        foreach (var item in majiangDic)
        {
            item.Value.Release();
        }
        slotBar.Init(lockSlotCount);

        majiangDic.Clear();
        OnGameStart();
        EnableCoverCube();
        StartCoroutine(CreateMajiangsInFrames(nums, topCount));

#if UNITY_EDITOR
        // var test = new int[] { 3, 5, 5, 4, 4 };//4,3,3 0.1  
        // var test = new int[] { 1, 1, 2, 3, 4, 5 };//1,5  0.6
        // var test = new int[] { 5, 5, 2, 1, 6 };//5,1,2  0.6
        // for (int i = 0; i < test.Length; i++)
        // {
        //     var maJiang = PoolMgr.Inst.Get<MaJiang>("MaJiang");
        //     maJiang.Num = test[i];
        //     maJiang.name = "MaJiang" + maJiang.Num;
        //     slotBar.Add(maJiang);
        // }
#endif
    }

    IEnumerator CreateMajiangsInFrames(List<int> nums, int topCount)
    {
        var majiangCount = nums.Count;

        var layerMajiangCount = 30;
        var totalLayer = 8;

        for (int i = 0; i < majiangCount; i++)
        {
            var layer = Mathf.FloorToInt(i / layerMajiangCount);
            Vector3 bornPos = bornArea.GetPosBottom2Top(layer, totalLayer - 1);
            CreateMajiang(bornPos, nums[i]);
            if (i % 10 == 0)
            {
                yield return new WaitForSeconds(0.05f);
            }
        }
        OnCreateMajiangComplete(topCount);
    }

    private void OnCreateMajiangComplete(int topCount)
    {
        Timer.Add(0.5f, 1, () =>
        {
            if (this == null) return;
            NotifyMgr.Event(NotifyNames.UpdateMajiangCount);
            EffectPool.Play("EffectFlip", 3);
            var totalCount = majiangDic.Count;
            var bottomCount = totalCount - topCount;
            var curCount = 0;
            foreach (var item in majiangDic)
            {
                curCount++;
                // if (curCount > bottomCount)
                // {
                _ = item.Value.transform.DORotateQuaternion(Quaternion.identity, 1f);
                // }
                // else
                // {
                //     if (Quaternion.Angle(item.Value.transform.rotation, Quaternion.identity) > 80)
                //     {
                //         _ = item.Value.transform.DORotateQuaternion(Quaternion.identity, 1f);
                //     }
                // }
            }
        });
    }
    private void CreateMajiang(Vector3 pos, int num, string prefabName = "MaJiang")
    {
        var maJiang = PoolMgr.Inst.Get<MaJiang>(prefabName);
        maJiang.transform.position = pos;
        maJiang.SetInfo(num);
        maJiang.name = "MaJiang" + maJiang.Num;
        // maJiang.GetComponent<Rigidbody>().isKinematic = true;
        majiangDic.Add(maJiang.GetHashCode(), maJiang);
    }

    public MaJiang GetMajiang(int id)
    {
        if (majiangDic.ContainsKey(id))
        {
            return majiangDic[id];
        }
        return null;
    }

    public void AddMajiang(MaJiang majinag)
    {
        majiangDic[majinag.GetHashCode()] = majinag;
        NotifyMgr.Event(NotifyNames.UpdateMajiangCount);
    }

    public void RemoveMajiang(int id)
    {
        if (majiangDic.ContainsKey(id))
        {
            majiangDic.Remove(id);
            NotifyMgr.Event(NotifyNames.UpdateMajiangCount);
        }
        else
        {
            Debug.LogWarning($"尝试移除不存在的麻将ID: {id}");
        }
    }

    private readonly int[] majiangItemIds = new int[] { ItemId.ItemBulb, ItemId.ItemShuffle, ItemId.ItemTurn };
    private void CreateMajiangItem()
    {
        var pos = itemArea.GetRandonPos();
        CreateMajiang(pos, majiangItemIds[UnityEngine.Random.Range(0, majiangItemIds.Length)], "MaJiangItem");
    }

    private void Update()
    {
        // _leftCreateItemMajiangTime -= Time.deltaTime;
        // if (_leftCreateItemMajiangTime < 0)
        // {
        //     _leftCreateItemMajiangTime = UnityEngine.Random.Range(createItemMajiangInterval.x, createItemMajiangInterval.y); ;
        //     if (GameGlobal.Level > 1 && _itemMajiangCount < itemMajiangMaxCount)
        //     {
        //         CreateMajiangItem();
        //     }
        //     _itemMajiangCount++;
        // }

        // 自动点击逻辑
        UpdateAutoClick();

        if (Input.GetMouseButtonDown(0))
        {
            if (IsTouchUI)
                return;
            var ray = mainCamera.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out RaycastHit hit, 100, clickLayerMask))
            {
                if (hit.collider.gameObject.TryGetComponent<MaJiang>(out var maJiang))
                {
                    Platform.Instance.VibrateShort();
                    SoundManager.PlayEffect("moveEnd");
                    if (slotBar.IsFull)
                    {
                        return;
                    }
                    maJiang.OnClick?.Invoke();
                    if (maJiang.Num > 1000)
                    {
                        NotifyMgr.Event(NotifyNames.SelectItemMajiang, maJiang.GetHashCode());
                    }
                    else
                    {
                        slotBar.Add(maJiang);
                    }
                }
                else if (hit.collider.gameObject.TryGetComponent<Slot>(out var slot))
                {
                    if (slot.IsLock)
                    {
                        var lockSlot = slotBar.GetFirstLockSlot();
                        if (lockSlot == null)
                            return;
                        battleHandler?.Pause();
                        if (lockSlot.IsOpenByShare)
                        {
                            Platform.Instance.Share(ShareType.openSlot, () =>
                            {
                                if (!isFirstOpenSlot)
                                {
                                    isFirstOpenSlot = true;
                                    GameGlobal.SwitchOpenSlotShareOrVideo();
                                }

                                battleHandler?.Resume();
                                slotBar.UnLockSlot();
                            });
                        }
                        else
                        {
                            Platform.Instance.ShowVideoAd(AdType.openSlot, () =>
                            {
                                if (!isFirstOpenSlot)
                                {
                                    isFirstOpenSlot = true;
                                    GameGlobal.SwitchOpenSlotShareOrVideo();
                                }

                                battleHandler?.Resume();
                                slotBar.UnLockSlot();
                            });
                        }
                    }
                }
            }
        }
    }
    private bool isFirstOpenSlot = false;
    private void OnDestroy()
    {
        if (Camera.main != null)
        {
            var cameraStack = Camera.main.GetUniversalAdditionalCameraData().cameraStack;
            if (cameraStack.Contains(mainCamera))
            {
                cameraStack.Remove(mainCamera);
            }
        }
    }

    internal void Test(int[] nums, float delay)
    {
        for (int i = 0; i < nums.Length; i++)
        {
            var majiangs = FindMajiang(nums[i], 1);
            if (majiangs == null)
                continue;
            for (int j = 0; j < majiangs.Length; j++)
            {
                var index = j;
                if (i != 0)
                {
                    DOVirtual.DelayedCall(delay, () =>
                    {
                        if (this == null) return;
                        slotBar.Add(majiangs[index]);
                    });
                }
                else
                {
                    slotBar.Add(majiangs[index]);
                }

            }
        }
    }

    internal bool MajinagIsClear
    {
        get
        {
            return GetMajinagWithItemCount() == 0;
        }
    }

    internal int GetMajinagTypeCount()
    {
        return typeCount;
    }
    internal int GetMajinagWithItemCount()
    {
        var count = 0;
        foreach (var item in majiangDic)
        {
            if (!item.Value.IsItemMajiang)
                count++;
        }
        return count;
    }

    /// <summary>
    /// 查找麻将，数字或数量不匹配都返回null
    /// </summary>
    /// <param name="num"></param>
    /// <param name="count"></param>
    /// <returns>MaJiang[]|null</returns>
    internal MaJiang[] FindMajiang(int num, int count)
    {
        var result = new MaJiang[count];
        var idx = 0;
        foreach (var item in majiangDic)
        {
            if (idx == count)
            {
                break;
            }
            var majiang = item.Value;
            if (majiang.IsItemMajiang)
                continue;

            if (majiang.Num == num)
            {
                result[idx] = majiang;
                idx++;
            }
        }
        if (idx < count)
            return null;

        return result;
    }

    internal bool UseBulbItem()
    {
        var (num, count, emptyCount) = slotBar.GetMaxCountNumAndCountAndEmptyCount();
        int needCount = 0;
        if (num == 0)
        {
            foreach (var majiang in majiangDic)
            {
                if (majiang.Value.IsItemMajiang)
                    continue;
                num = majiang.Value.Num;
                needCount = 3;
                break;
            }
        }
        else
        {
            needCount = 3 - count;
            if (needCount > emptyCount)
            {
                TipMgr.ShowTip(LangUtil.GetText("txtPositionNotEnough")); // 位置不够，无法使用哦~
                return false;
            }
        }
        // Debug.Log("num: " + num + "  count: " + count + "  emptyCount: " + emptyCount);

        if (needCount == 0)
            return false;

        MaJiang[] majiangs = FindMajiang(num, needCount);
        if (majiangs == null)
            return false;

        for (int i = 0; i < majiangs.Length; i++)
        {
            var majiang = majiangs[i];
            if (majiang == null)
            {
                Debug.LogWarning("麻将数量不匹配！");
                continue;
            }
            slotBar.Add(majiang);
        }
        return true;
    }

    public void UseTurnItem(bool justTurnAngle80 = false)
    {
        EffectPool.Play("EffectFlip", 3);

        foreach (var item in majiangDic)
        {
            if (justTurnAngle80)
            {
                if (Quaternion.Angle(item.Value.transform.rotation, Quaternion.identity) > 80)
                {
                    _ = item.Value.transform.DORotateQuaternion(Quaternion.identity, 1f);
                }
            }
            else
            {
                _ = item.Value.transform.DORotateQuaternion(Quaternion.identity, 1f);
            }
        }
    }

    public void UseShuffleItem()
    {
        coverCube.SetActive(true);
        shuffleCube.SetActive(true);
        shuffleCube.transform.DORotate(new(0, 360 * 2, 0), 3f, RotateMode.WorldAxisAdd).SetEase(Ease.Linear).OnComplete(() =>
        {
            if (this == null)
                return;
            UseTurnItem(true);
            shuffleCube.SetActive(false);
            DOVirtual.DelayedCall(1f, () =>
            {
                if (this == null)
                    return;
                coverCube.SetActive(false);
            });
        });

        // shuffleCube.transform.DORotateQuaternion(Quaternion.Euler(0, 360 * 3, 0), 3f).SetEase(Ease.Linear).OnComplete(() =>
        // {
        //     coverCube.SetActive(false);
        //     shuffleCube.SetActive(false);
        // });
    }
    public bool UseMagnetItem()
    {
        var isUsed = false;
        for (int idx = 0; idx < 3; idx++)
        {
            int num = 0;
            foreach (var item in majiangDic)
            {
                if (item.Value.IsItemMajiang)
                    continue;

                num = item.Value.Num;
                if (num == 0)
                    continue;

                MaJiang[] majiangs = FindMajiang(num, 3);
                if (majiangs == null)
                    continue;

                isUsed = true;
                for (int i = 0; i < majiangs.Length; i++)
                {
                    var majiang = majiangs[i];
                    if (majiang == null)
                    {
                        Debug.LogWarning("麻将数量不匹配！");
                        continue;
                    }
                    RemoveMajiang(majiang.GetHashCode());

                    majiang.EnableTouch(false);
                    var startPos = majiang.transform.position;
                    majiang.transform.DOMove(startPos + Vector3.up * 1.1f, 0.2f, false).SetDelay(0.2f).SetEase(Ease.Linear).OnComplete(() =>
                    {
                        if (this == null)
                            return;
                        majiang.transform.DOPunchScale(Vector3.one * 0.2f, 0.5f, 10, 1).OnComplete(() =>
                        {
                            if (this == null)
                                return;

                            DOVirtual.DelayedCall(0.5f, () =>
                            {
                                if (this == null)
                                    return;
                                majiang.Release();
                            });
                        });
                    });
                }

                DOVirtual.DelayedCall(1.4f, () =>
                {
                    slotBar.CheckWin();
                });
                break;
            }
        }
        return isUsed;
    }

    public void ClearSlotBar()
    {
        slotBar.Clear2Table();
        DOVirtual.DelayedCall(0.5f, () =>
        {
            if (this == null)
                return;
            UseTurnItem();
        });
    }

    internal void MoveBar(float targetZ)
    {
        // for (int i = 0; i < slotBar.slots.Length; i++)
        // {
        //     var slot = slotBar.slots[i];
        //     var pos = slot.transform.position;
        //     slot.MoveBar(new Vector3(pos.x, pos.y, targetZ));
        // }
        var pos = slotBar.transform.position;
        slotBar.transform.position = new Vector3(pos.x, pos.y, targetZ);
        for (int i = 0; i < slotBar.slots.Length; i++)
        {
            var slot = slotBar.slots[i];
            // var pos = slot.transform.position;
            // slot.MoveBar(new Vector3(pos.x, pos.y, targetZ));
            slot.UpdateInitPos();
        }
    }

    private bool IsTouchUI
    {
        get
        {
            return GRoot.inst.touchTarget != null;
        }
    }

    // 自动点击相关
    public bool isAutoClickEnabled = false;
    public float autoClickInterval = 0.2f;
    private float lastAutoClickTime = 0f;

    public void SetAutoClick(bool enabled)
    {
        isAutoClickEnabled = enabled;
        lastAutoClickTime = Time.time;
    }

    public void SetAutoClickSpeed(float speed)
    {
        autoClickInterval = Mathf.Clamp(1f / speed, 0.1f, 2f); // 速度范围：0.5x到10x
    }

    public bool IsAutoClickEnabled => isAutoClickEnabled;

    public int GetClickableMajiangCount()
    {
        return GetClickableMajiangs().Count;
    }

    public void ForceCleanupMajiangs()
    {
        CleanupInvalidMajiangs();
    }

    public void ForceRebuildDictionary()
    {
        RebuildMajiangDictionary();
    }

    private void UpdateAutoClick()
    {
        if (!isAutoClickEnabled || slotBar.IsFull)
            return;

        // 动态调整点击间隔：槽位越满，点击越慢
        float dynamicInterval = autoClickInterval;
        // var (_, _, emptyCount) = slotBar.GetMaxCountNumAndCountAndEmptyCount();
        // if (emptyCount <= 2)
        // {
        //     dynamicInterval = autoClickInterval * 2f; // 槽位快满时放慢速度
        // }
        // else if (emptyCount <= 1)
        // {
        //     dynamicInterval = autoClickInterval * 3f; // 槽位很满时更慢
        // }

        if (Time.time - lastAutoClickTime < dynamicInterval)
            return;

        var bestMajiang = FindBestClickableMajiang();
        if (bestMajiang != null)
        {
            lastAutoClickTime = Time.time;
            Debug.Log($"自动点击麻将: {bestMajiang.Num}, 字典总数: {majiangDic.Count}");
            ClickMajiang(bestMajiang);
        }
        else
        {
            // 没有找到可点击的麻将，检查是否有数据不同步的问题
            int activeMajiangs = 0;
            foreach (var mj in FindObjectsOfType<MaJiang>())
            {
                if (mj.gameObject.activeInHierarchy && !mj.IsItemMajiang)
                    activeMajiangs++;
            }

            if (majiangDic.Count == 0 && activeMajiangs > 0)
            {
                Debug.LogWarning($"数据不同步！字典为空但场景中还有 {activeMajiangs} 个活跃麻将");
                // 尝试重新构建字典
                RebuildMajiangDictionary();
            }
        }
    }

    private MaJiang FindBestClickableMajiang()
    {
        var clickableMajiangs = GetClickableMajiangs();
        if (clickableMajiangs.Count == 0)
            return null;

        // 获取当前槽位状态
        var (maxNum, maxCount, _) = slotBar.GetMaxCountNumAndCountAndEmptyCount();

        // 策略1: 优先选择槽位中已有的麻将类型（可以直接消除）
        if (maxNum > 0 && maxCount == 2)
        {
            foreach (var majiang in clickableMajiangs)
            {
                if (majiang.Num == maxNum)
                    return majiang;
            }
        }

        // 策略2: 如果槽位中有1个某种麻将，优先选择相同类型
        if (maxNum > 0 && maxCount == 1)
        {
            foreach (var majiang in clickableMajiangs)
            {
                if (majiang.Num == maxNum)
                    return majiang;
            }
        }

        // 策略3: 选择可点击麻将中数量最多的类型
        var numCounts = new Dictionary<int, int>();
        foreach (var majiang in clickableMajiangs)
        {
            if (!numCounts.ContainsKey(majiang.Num))
                numCounts[majiang.Num] = 0;
            numCounts[majiang.Num]++;
        }

        // 找到数量最多且至少有3个的麻将类型
        int bestNum = 0;
        int bestCount = 0;
        foreach (var pair in numCounts)
        {
            if (pair.Value >= 3 && pair.Value > bestCount)
            {
                bestNum = pair.Key;
                bestCount = pair.Value;
            }
        }

        // 如果没有找到3个以上的，选择数量最多的
        if (bestNum == 0)
        {
            foreach (var pair in numCounts)
            {
                if (pair.Value > bestCount)
                {
                    bestNum = pair.Key;
                    bestCount = pair.Value;
                }
            }
        }

        // 返回该类型的第一个麻将
        foreach (var majiang in clickableMajiangs)
        {
            if (majiang.Num == bestNum)
                return majiang;
        }

        return clickableMajiangs[0];
    }

    private List<MaJiang> GetClickableMajiangs()
    {
        return majiangDic.Values.ToList();

        var clickableMajiangs = new List<MaJiang>();

        // 先清理无效的麻将引用
        CleanupInvalidMajiangs();

        foreach (var pair in majiangDic)
        {
            var majiang = pair.Value;
            if (majiang == null || majiang.IsItemMajiang || !majiang.boxCollider.enabled)
                continue;

            if (IsMajiangClickable(majiang))
            {
                clickableMajiangs.Add(majiang);
            }
        }

        return clickableMajiangs;
    }

    private void CleanupInvalidMajiangs()
    {
        var keysToRemove = new List<int>();

        foreach (var pair in majiangDic)
        {
            var majiang = pair.Value;
            // 检查麻将对象是否已被销毁或回收到对象池
            if (majiang == null || !majiang.gameObject.activeInHierarchy)
            {
                keysToRemove.Add(pair.Key);
            }
        }

        foreach (var key in keysToRemove)
        {
            majiangDic.Remove(key);
            Debug.Log($"清理无效麻将引用: {key}");
        }

        if (keysToRemove.Count > 0)
        {
            Debug.Log($"清理了 {keysToRemove.Count} 个无效麻将引用，剩余: {majiangDic.Count}");
            NotifyMgr.Event(NotifyNames.UpdateMajiangCount);
        }
    }

    private void RebuildMajiangDictionary()
    {
        Debug.Log("重新构建麻将字典...");
        majiangDic.Clear();

        var allMajiangs = FindObjectsOfType<MaJiang>();
        foreach (var majiang in allMajiangs)
        {
            if (majiang.gameObject.activeInHierarchy && !majiang.IsItemMajiang)
            {
                majiangDic[majiang.GetHashCode()] = majiang;
            }
        }

        Debug.Log($"重新构建完成，字典中有 {majiangDic.Count} 个麻将");
        NotifyMgr.Event(NotifyNames.UpdateMajiangCount);
    }

    private bool IsMajiangClickable(MaJiang targetMajiang)
    {
        // 使用射线检测判断麻将是否可点击
        var camera = mainCamera;
        var screenPos = camera.WorldToScreenPoint(targetMajiang.transform.position);
        var ray = camera.ScreenPointToRay(screenPos);

        if (Physics.Raycast(ray, out RaycastHit hit, 100, clickLayerMask))
        {
            var hitMajiang = hit.collider.GetComponent<MaJiang>();
            return hitMajiang == targetMajiang;
        }

        return false;
    }

    private void ClickMajiang(MaJiang maJiang)
    {
        Platform.Instance.VibrateShort();
        SoundManager.PlayEffect("moveEnd");

        maJiang.OnClick?.Invoke();
        if (maJiang.Num > 1000)
        {
            NotifyMgr.Event(NotifyNames.SelectItemMajiang, maJiang.GetHashCode());
        }
        else
        {
            slotBar.Add(maJiang);
        }
    }
}