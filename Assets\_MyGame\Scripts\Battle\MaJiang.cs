using System;
using System.Collections.Generic;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Core.PathCore;
using DG.Tweening.Plugins.Options;
using UnityEngine;

public class MaJiang : PoolItem
{
    public Action OnClick;
    public Vector2Int TexTile = new(9, 7);
    public float slotScale = 0.8f;
    // public TextMeshPro txtNum;
    private int _num;
    public int Num
    {
        get
        {
            return _num;
        }
    }

    private MajiangAnimState _animState = MajiangAnimState.Idle;
    public bool IsMoving { get { return _animState != MajiangAnimState.Idle; } }

    private enum MajiangAnimState
    {
        Idle,
        Flying,
        Jumping,
        Punching,
        Merging
    }

    public override int GetHashCode()
    {
        return base.GetHashCode();
    }
    private Queue<AnimationAction> animationQueue = new Queue<AnimationAction>();
    private bool isAnimationPlaying = false;

    public Collider boxCollider;
    Rigidbody rigBody;
    private Tweener punchTween;
    private Material huaMat;
    private string sound;
    public int testNum;
    private void Awake()
    {
        rigBody = GetComponent<Rigidbody>();
        boxCollider = GetComponent<BoxCollider>();
        var renderer = GetComponentInChildren<Renderer>();
        var materials = renderer.materials;
        if (materials.Length > 1)
        {
            var bgMat = materials[0];
            huaMat = materials[1];
        }
        else
        {
            huaMat = materials[0];
        }
        if (testNum != 0)
        {
            Debug.LogWarning("=============testNum: " + testNum);
            SetTex(testNum - 1, 0);
            EnableTouch(false);
        }
    }

    internal void SetInfo(int num)
    {
        _num = num;

        var info = ConfigMajiang.GetData(num);
        if (info == null)
        {
            // if (txtNum != null)
            // {
            //     txtNum.text = _num.ToString();
            // }
            return;
        }

        sound = info.sound;
        SetTex(info.posIndex, info.textureIndex);
    }

    public void SetTex(int index, int textureIndex)
    {
        var idx = index;
        var row = idx / TexTile.x;
        var col = idx % TexTile.x;

        AssetBundleManager.LoadTexture("Textures/majiang" + textureIndex, (texture) =>
        {
            huaMat.mainTexture = texture;
            var tileOffset = new Vector2(col * 1f / TexTile.x, -row * 1f / TexTile.y);
            huaMat.mainTextureOffset = tileOffset;
            if (textureIndex == 2)
            {
                //动物贴图
                huaMat.SetFloat("_Smoothness", 0.6f);
            }
            AssetBundleManager.LoadTexture("Textures/majiang_n" + textureIndex, (noarmalTexture) =>
            {
                huaMat.SetTexture("_Normal", noarmalTexture);
                huaMat.SetVector("_Normal_ST", new Vector4(1, 1, tileOffset.x, tileOffset.y));
            });
        });
    }
    public bool IsItemMajiang
    {
        get => Num > 1000;
    }
    internal void PlayMergeSound()
    {
        if (string.IsNullOrEmpty(sound))
            return;
        SoundManager.PlayEffect(sound);
    }

    internal TweenerCore<Vector3, Path, PathOptions> CurveMove(Vector3 targetPos, float moveDuration = 0.5f, float height = 2, float ctrlPosDisPercent = 0.5f, float scale = 1)
    {
        SetAnimState(MajiangAnimState.Flying);

        var startPos = transform.position;
        var moveDir = targetPos - startPos;
        var delta = new Vector3(moveDir.x * 0.5f, height, moveDir.z * ctrlPosDisPercent);
        Vector3 controlPoint = startPos + delta;

        var posAry = new Vector3[] { startPos, controlPoint, targetPos };
        _ = transform.DORotateQuaternion(Quaternion.identity, moveDuration * ctrlPosDisPercent);
        _ = transform.DOScale(Vector3.one * scale, moveDuration - moveDuration * ctrlPosDisPercent).SetDelay(moveDuration * ctrlPosDisPercent);
        return transform.DOPath(posAry, moveDuration, PathType.CatmullRom, PathMode.Full3D, 10, Color.red).SetEase(Ease.Linear);
    }

    internal TweenerCore<Vector3, Path, PathOptions> JumpMove(Vector3 targetPos, float moveDuration, float height = 1, float delay = 0)
    {
        // transform.DOKill(true);
        var startPos = transform.position;
        Vector3 controlPoint = startPos + (targetPos - startPos) * 0.5f + Vector3.forward * height;
        var posAry = new Vector3[] { startPos, controlPoint, targetPos };
        return transform.DOPath(posAry, moveDuration, PathType.CatmullRom, PathMode.Full3D, 10, Color.red).SetDelay(delay).SetEase(Ease.Linear);
    }

    internal Tweener PunchMove(float punch, float duration, int vibrato = 10, float elasticity = 1f)
    {
        var action = new PunchMoveAction(punch, duration, vibrato, elasticity);
        EnqueueAnimation(action);
        return punchTween;
    }

    internal void EnableTouch(bool value)
    {
        if (this == null)
            return;
        boxCollider.enabled = value;
        rigBody.isKinematic = !value;
    }

    private bool IsAnimating(MajiangAnimState state) => _animState == state;
    private void SetAnimState(MajiangAnimState state)
    {
        if (_animState != state)
        {
            _animState = state;
        }
    }

    private void Update()
    {
        if (!isAnimationPlaying && animationQueue.Count > 0)
        {
            PlayNextAnimation();
        }
    }

    private void PlayNextAnimation()
    {
        if (animationQueue.Count == 0)
        {
            isAnimationPlaying = false;
            return;
        }

        isAnimationPlaying = true;
        AnimationAction action = animationQueue.Dequeue();
        action.Execute(this, OnAnimationComplete);
    }

    private void OnAnimationComplete()
    {
        isAnimationPlaying = false;
        if (animationQueue.Count > 0)
        {
            PlayNextAnimation();
        }
    }

    private void EnqueueAnimation(AnimationAction action)
    {
        animationQueue.Enqueue(action);
    }

    internal void FlyToSlot(Slot slot, float moveDuration, Action<bool> flyDoneCallback)
    {
        EnqueueAnimation(new FlyToSlotAction(slot, moveDuration, flyDoneCallback));
    }

    internal void FlyToTable(Vector3 targetPos)
    {
        EnqueueAnimation(new FlyToTableAction(targetPos));
    }

    internal void JumpToSlot(Slot slot, float moveDuration, float delay, Action jumpDoneCallback)
    {
        EnqueueAnimation(new JumpToSlotAction(slot, moveDuration, delay, jumpDoneCallback));
    }

    internal static void PlayMerge(Slot leftSlot, Slot centerSlot, Slot rightSlot, Action onMergeComplete)
    {
        var leftMajiang = leftSlot.GetMajiang();
        var centerMajiang = centerSlot.GetMajiang();
        var rightMajiang = rightSlot.GetMajiang();

        // 清除槽位中的麻将引用
        leftSlot.ClearMajiang();
        centerSlot.ClearMajiang();
        rightSlot.ClearMajiang();

        // 增加连击计数
        GameGlobal.IncrementComboCount(1);
        var comboCount = GameGlobal.ComboCount > ConfigSetting.Setting.maxComboCount ? ConfigSetting.Setting.maxComboCount : GameGlobal.ComboCount;
        GameGlobal.IncrementLevelStar(comboCount);

        // 停止之前的动画
        // leftSlot.transform.DOKill(true);
        // centerSlot.transform.DOKill(true);

        // 使用同步合并动画
        centerMajiang.EnqueueAnimation(new SynchronizedMergeAction(leftSlot, centerSlot, rightSlot,
                                                                leftMajiang, centerMajiang, rightMajiang, onMergeComplete));
    }

    public override void OnReleaseToPool()
    {
        transform.DOKill();
        SetAnimState(MajiangAnimState.Idle);
        animationQueue.Clear();
        isAnimationPlaying = false;
        transform.localRotation = Quaternion.identity;
        transform.localScale = Vector3.one;
        base.OnReleaseToPool();
    }



    #region 动画行为类
    // 动画行为基类
    private abstract class AnimationAction
    {
        public abstract void Execute(MaJiang majiang, Action onActionComplete);
    }

    // 同步合并动画行为类
    private class SynchronizedMergeAction : AnimationAction
    {
        private Slot leftSlot;
        private Slot centerSlot;
        private Slot rightSlot;
        private MaJiang leftMajiang;
        private MaJiang centerMajiang;
        private MaJiang rightMajiang;
        private Action onMergeComplete;

        private float arrivedDuration = 0.15f;
        private float moveTopDuration = 0.15f;
        private float moveCenterDuration = 0.15f;
        private float offsetX = 0.3f;
        private Vector3 flyPos = new Vector3(0, 0, 0.4f);

        public SynchronizedMergeAction(Slot leftSlot, Slot centerSlot, Slot rightSlot,
                                      MaJiang leftMajiang, MaJiang centerMajiang, MaJiang rightMajiang,
                                      Action onMergeComplete)
        {
            this.leftSlot = leftSlot;
            this.centerSlot = centerSlot;
            this.rightSlot = rightSlot;
            this.leftMajiang = leftMajiang;
            this.centerMajiang = centerMajiang;
            this.rightMajiang = rightMajiang;
            this.onMergeComplete = onMergeComplete;
        }

        public override void Execute(MaJiang majiang, Action onActionComplete)
        {
            // 设置所有麻将的动画状态
            leftMajiang.SetAnimState(MajiangAnimState.Merging);
            centerMajiang.SetAnimState(MajiangAnimState.Merging);
            rightMajiang.SetAnimState(MajiangAnimState.Merging);

            var centerPos = centerSlot.GetMajiangPos() + flyPos;

            // 创建一个主序列来同步所有动画
            Sequence masterSequence = DOTween.Sequence();

            // 左侧麻将动画
            Sequence leftSequence = DOTween.Sequence();
            leftSequence.Append(leftSlot.PunchMove(-0.1f, arrivedDuration, 3, 0.2f));
            leftSequence.Insert(0.1f, leftMajiang.transform.DOMove(leftMajiang.transform.position + flyPos - new Vector3(offsetX, 0, 0), moveTopDuration));
            leftSequence.Append(leftMajiang.transform.DOMove(centerPos, moveCenterDuration));

            // 中间麻将动画
            Sequence centerSequence = DOTween.Sequence();
            centerSequence.Append(centerSlot.PunchMove(-0.1f, arrivedDuration, 3, 0.2f).SetDelay(moveTopDuration * 0.5f));
            centerSequence.Insert(0.3f, centerMajiang.transform.DOMove(centerPos, moveTopDuration));
            centerSequence.AppendCallback(() => { centerMajiang.PlayMergeSound(); });
            centerSequence.Append(centerMajiang.transform.DOPunchScale(Vector3.one * 0.2f, moveCenterDuration, 3, 0));

            // 右侧麻将动画
            Sequence rightSequence = DOTween.Sequence();
            rightSequence.Insert(0.2f, rightMajiang.transform.DOMove(rightMajiang.transform.position + flyPos + new Vector3(offsetX, 0, 0), moveTopDuration));
            rightSequence.Append(rightMajiang.transform.DOMove(centerPos, moveCenterDuration));

            // 将所有序列添加到主序列中
            masterSequence.Append(DOTween.Sequence()
                .Join(leftSequence)
                .Join(centerSequence)
                .Join(rightSequence));

            // 完成后的回调
            masterSequence.AppendCallback(() =>
            {
                Platform.Instance.VibrateShort();
                SoundManager.PlayEffect("merge");
                var effect = PoolMgr.Inst.Get("explosions");
                var mergePos = centerMajiang.transform.position;

                effect.transform.position = mergePos;
                DOVirtual.DelayedCall(0.5f, () =>
                {
                    effect.Release();
                });

                // 释放所有麻将
                leftMajiang.Release();
                centerMajiang.Release();
                rightMajiang.Release();

                NotifyMgr.Event(NotifyNames.MergeDone, mergePos);

                // 调用合并完成回调
                onMergeComplete?.Invoke();
                onActionComplete?.Invoke();
            });
        }
    }

    // 飞向槽位动画
    private class FlyToSlotAction : AnimationAction
    {
        private Slot slot;
        private float moveDuration;
        private Action<bool> flyDoneCallback;

        public FlyToSlotAction(Slot slot, float moveDuration, Action<bool> flyDoneCallback)
        {
            this.slot = slot;
            this.moveDuration = moveDuration;
            this.flyDoneCallback = flyDoneCallback;
        }

        public override void Execute(MaJiang majiang, Action onActionComplete)
        {
            majiang.SetAnimState(MajiangAnimState.Flying);
            majiang.EnableTouch(false);
            majiang.CurveMove(slot.GetMajiangPos(), moveDuration, 2f, 0.15f, majiang.slotScale).OnComplete(() =>
            {
                majiang.SetAnimState(MajiangAnimState.Idle);
                flyDoneCallback?.Invoke(true);
                onActionComplete?.Invoke();
            });
        }
    }

    // 飞向桌面动画
    private class FlyToTableAction : AnimationAction
    {
        private Vector3 targetPos;
        private float moveDuration = 0.5f;

        public FlyToTableAction(Vector3 targetPos)
        {
            this.targetPos = targetPos;
        }

        public override void Execute(MaJiang majiang, Action onActionComplete)
        {
            majiang.EnableTouch(false);
            majiang.CurveMove(targetPos, moveDuration, 2f, 0.5f, 1).OnComplete(() =>
            {
                majiang.EnableTouch(true);
                onActionComplete?.Invoke();
            });
        }
    }

    // 跳跃到槽位动画
    private class JumpToSlotAction : AnimationAction
    {
        private Slot slot;
        private float moveDuration;
        private float delay;
        private Action jumpDoneCallback;

        public JumpToSlotAction(Slot slot, float moveDuration, float delay, Action jumpDoneCallback)
        {
            this.slot = slot;
            this.moveDuration = moveDuration;
            this.delay = delay;
            this.jumpDoneCallback = jumpDoneCallback;
        }

        public override void Execute(MaJiang majiang, Action onActionComplete)
        {
            majiang.SetAnimState(MajiangAnimState.Jumping);
            majiang.transform.position = slot.GetMajiangPos();
            // majiang.JumpMove(slot.GetMajiangPos(), moveDuration, 0.2f, delay).OnComplete(() =>
            // {
            majiang.SetAnimState(MajiangAnimState.Idle);
            jumpDoneCallback?.Invoke();
            onActionComplete?.Invoke();
            // });
        }
    }

    // 弹跳动画
    private class PunchMoveAction : AnimationAction
    {
        private float punch;
        private float duration;
        private int vibrato;
        private float elasticity;

        public PunchMoveAction(float punch, float duration, int vibrato = 10, float elasticity = 1f)
        {
            this.punch = punch;
            this.duration = duration;
            this.vibrato = vibrato;
            this.elasticity = elasticity;
        }

        public override void Execute(MaJiang majiang, Action onComplete)
        {
            // majiang.transform.DOKill(true);
            majiang.SetAnimState(MajiangAnimState.Punching);
            majiang.punchTween = majiang.transform.DOPunchPosition(new Vector3(0, 0, punch), duration, vibrato, elasticity).OnComplete(() =>
            {
                majiang.SetAnimState(MajiangAnimState.Idle);
                onComplete?.Invoke();
            });
        }
    }


    #endregion
}