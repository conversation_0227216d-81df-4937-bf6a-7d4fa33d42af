using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(SystemManager))]
public class SystemManagerEditor : Editor
{
    private bool showGlobalSystems = true;
    private bool showGameplaySystems = true;
    private GUIStyle headerStyle;
    private GUIStyle systemStyle;

    private void InitStyles()
    {
        if (headerStyle == null)
        {
            headerStyle = new GUIStyle(EditorStyles.foldout)
            {
                fontStyle = FontStyle.Bold,
                fontSize = 12
            };
        }

        if (systemStyle == null)
        {
            systemStyle = new GUIStyle(EditorStyles.label)
            {
                padding = new RectOffset(20, 0, 0, 0)
            };
        }
    }

    public override void OnInspectorGUI()
    {
        InitStyles();

        SystemManager manager = (SystemManager)target;
        serializedObject.Update();

        EditorGUILayout.Space(10);
        var globalSystemsProp = serializedObject.FindProperty("debugGlobalSystemNames");
        var gameplaySystemsProp = serializedObject.FindProperty("debugGameplaySystemNames");

        // Global Systems
        showGlobalSystems = EditorGUILayout.Foldout(showGlobalSystems, "Global Systems", true, headerStyle);
        if (showGlobalSystems)
        {
            if (globalSystemsProp.arraySize == 0)
            {
                EditorGUILayout.LabelField("No Global Systems", systemStyle);
            }
            else
            {
                for (int i = 0; i < globalSystemsProp.arraySize; i++)
                {
                    EditorGUILayout.LabelField(globalSystemsProp.GetArrayElementAtIndex(i).stringValue, systemStyle);
                }
            }
        }

        EditorGUILayout.Space(10);

        // Gameplay Systems
        showGameplaySystems = EditorGUILayout.Foldout(showGameplaySystems, "Gameplay Systems", true, headerStyle);
        if (showGameplaySystems)
        {
            if (gameplaySystemsProp.arraySize == 0)
            {
                EditorGUILayout.LabelField("No Gameplay Systems", systemStyle);
            }
            else
            {
                for (int i = 0; i < gameplaySystemsProp.arraySize; i++)
                {
                    EditorGUILayout.LabelField(gameplaySystemsProp.GetArrayElementAtIndex(i).stringValue, systemStyle);
                }
            }
        }

        serializedObject.ApplyModifiedProperties();
    }
}
