using System;
using FairyGUI;
using LitJson;
using UnityEngine;

public class WxRankPanel : Panel
{
    private static Texture2D rankTexture;
    public WxRankPanel()
    {
        packName = "Common";
        compName = "WxRankPanel";
        modal = true;
    }

    private GTextField txtEmptyTip;
    private GList listRank;
    private Controller c1;
    private GButton btnUpdateRank;
    private GLoader imgRank;
    protected override void DoInitialize()
    {
        c1 = contentPane.GetController("c1");
        c1.onChanged.Add(OnTabChanged);

        imgRank = contentPane.GetChild("imgRank").asLoader;
        btnUpdateRank = contentPane.GetChild("btnUpdateRank").asButton;
        txtEmptyTip = contentPane.GetChild("txtEmptyTip").asTextField;
        listRank = contentPane.GetChild("listRank").asList;
        listRank.SetVirtual();
        listRank.itemRenderer = UpdateRankItem;

        imgRank.scaleY *= -1;
        imgRank.y += imgRank.height;

#if UNITY_EDITOR && WXGAME
        //编辑器下测试排行榜
        Platform.Set<PlatformWxgame>().GetRankList(UpdateRankData);
#endif

#if WXGAME && !UNITY_EDITOR
        Platform.Get<PlatformWxgame>().ReportScore(StorageMgr.Level);
        OnTabChanged(null);
#endif

    }

    private bool preViewFriendRank;
    private void OnTabChanged(EventContext context)
    {
#if WXGAME && !UNITY_EDITOR
        if (c1.selectedIndex == 0)
        {
            //世界榜
            if (preViewFriendRank)
            {
                WeChatWASM.WX.HideOpenData();
            }
            if (string.IsNullOrEmpty(GameGlobal.OpenId))
            {
                var pos = btnUpdateRank.parent.LocalToGlobal(new Vector2(btnUpdateRank.x, btnUpdateRank.y));
                var btnWidth = btnUpdateRank.width * Stage.inst.width / 720;
                var btnHeight = btnUpdateRank.height * Stage.inst.height / 1280;

                Platform.Get<PlatformWxgame>().CreateUserInfoButton(pos.x, pos.y, btnWidth, btnHeight, (code) =>
                {
                    if (!IsShowing || code != 0)
                        return;

                    TipMgr.ShowTip(LangUtil.GetText("txtReportSuccess")); // 上报成功!
                    Platform.Get<PlatformWxgame>().HideUserInfoButton();
                    Platform.Get<PlatformWxgame>().GetRankList(UpdateRankData);
                });
            }
            else
            {
                UpdateRank(false);
            }
            Platform.Get<PlatformWxgame>().GetRankList(UpdateRankData);
        }
        else
        {
            // var isAccept = await Platform.Get<PlatformWxgame>().RequestAuthorize(PlatformWxgame.Scope.WxFriendInteraction);
            // if (!isAccept)
            //     return;

            //好友榜
            Platform.Get<PlatformWxgame>().HideUserInfoButton();
            preViewFriendRank = true;
            if (rankTexture == null)
            {
                rankTexture = new Texture2D(512, (int)(512 * imgRank.height / imgRank.width));
            }

            var pos = imgRank.parent.LocalToGlobal(new Vector2(imgRank.x, imgRank.y));
            var rankWidth = imgRank.width * Stage.inst.width / 720f;//832
            WeChatWASM.WX.ShowOpenData(rankTexture, (int)pos.x, (int)pos.y, (int)rankWidth, (int)(rankWidth * imgRank.height / imgRank.width));
            imgRank.texture = new NTexture(rankTexture);
            Platform.Get<PlatformWxgame>().ShowFriendsRank();
        }
#endif
    }

    private void UpdateRank(bool withTip = true)
    {
#if WXGAME && !UNITY_EDITOR
        Platform.Get<PlatformWxgame>().UpdateRank(GameGlobal.OpenId, (code) =>
        {
            if (!IsShowing || code != 0)
                return;

            if (withTip)
            {
                TipMgr.ShowTip(LangUtil.GetText("txtReportSuccess")); // 上报成功!
            }
            Platform.Get<PlatformWxgame>().GetRankList(UpdateRankData);
        });
#endif
    }

    private void UpdateRankItem(int index, GObject item)
    {
        var txtRank = item.asCom.GetChild("txtRank").asTextField;
        var txtName = item.asCom.GetChild("txtName").asTextField;
        var txtScore = item.asCom.GetChild("txtScore").asTextField;
        var icon = item.asCom.GetChild("icon").asLoader;
        var imgBg = item.asCom.GetChild("imgBg").asImage;
        if (rankData == null)
            return;
        var data = rankData[index];

        var rank = index + 1;
        Color bgColor = Color.white;
        if (rank == 1) ColorUtility.TryParseHtmlString("#FFD700", out bgColor);
        else if (rank == 2) ColorUtility.TryParseHtmlString("#C0C0C0", out bgColor);
        else if (rank == 3) ColorUtility.TryParseHtmlString("#CD7F32", out bgColor);

        imgBg.color = bgColor;

        txtRank.text = rank.ToString();
        icon.url = JsonUtil.ToString(data, "avatarUrl");
        var tf = txtName.textFormat;
        tf.font = "wxfont";
        txtName.textFormat = tf;
        txtName.text = JsonUtil.ToString(data, "nickname");
        txtScore.text = JsonUtil.ToString(data, "level");
    }

    private JsonData rankData;
    private void UpdateRankData(JsonData res)
    {
        if (!IsShowing)
            return;

        var code = JsonUtil.ToInt(res, "code");
        if (code != 0)
        {
            Debug.LogWarning("GetRankData:" + JsonUtil.ToInt(res, "message"));
            txtEmptyTip.visible = true;
            return;
        }

        rankData = JsonUtil.ToJson(res, "data");
        if (rankData.Count == 0)
        {
            txtEmptyTip.visible = true;
        }
        listRank.numItems = rankData.Count;
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                this.Hide();
                break;
            case "btnUpdateRank":
                UpdateRank();
                break;
        }
    }

    protected override void OnHide()
    {
#if WXGAME && !UNITY_EDITOR
        WeChatWASM.WX.HideOpenData();
        Platform.Get<PlatformWxgame>().HideUserInfoButton();
#endif
    }
}

