/// <summary>
/// 麻将合并规则接口
/// </summary>
public interface IMergeRule
{
    /// <summary>
    /// 检查三个槽位的麻将是否可以合并
    /// </summary>
    bool CanMerge(Slot leftSlot, Slot centerSlot, Slot rightSlot);

    /// <summary>
    /// 查找新麻将的最佳插入位置
    /// </summary>
    int FindInsertPosition(Slot[] slots, int useSlotCount, int newNum);
}

/// <summary>
/// 相同数字合并规则
/// </summary>
public class SameNumberRule : IMergeRule
{
    public bool CanMerge(Slot leftSlot, Slot centerSlot, Slot rightSlot)
    {
        // 检查三个槽位是否都有麻将且数字相同
        return centerSlot.Num != 0 && leftSlot.Num == centerSlot.Num && centerSlot.Num == rightSlot.Num;
    }

    public int FindInsertPosition(Slot[] slots, int useSlotCount, int newNum)
    {
        var insterIndex = -1; // 没有找到合适的位置返回-1
        for (int i = useSlotCount - 1; i >= 0; i--)
        {
            var slot = slots[i];
            if (slot.IsEmpty)
            {
                insterIndex = i;
            }
            else
            {
                if (slot.Num == newNum)
                {
                    insterIndex = i + 1;
                    break;
                }
            }
        }
        return insterIndex;
    }
}

/// <summary>
/// 合并规则管理器
/// </summary>
public class MergeRuleManager
{
    private IMergeRule activeRule;
    public MergeRuleManager()
    {
        activeRule = new SameNumberRule();
    }

    /// <summary>
    /// 检查是否可以合并
    /// </summary>
    public bool CanMerge(Slot leftSlot, Slot centerSlot, Slot rightSlot)
    {
        if (activeRule.CanMerge(leftSlot, centerSlot, rightSlot))
        {
            return true;
        }
        return false;
    }

    /// <summary>
    /// 查找最佳插入位置
    /// </summary>
    public int FindInsertPosition(Slot[] slots, int useSlotCount, int newNum)
    {
        return activeRule.FindInsertPosition(slots, useSlotCount, newNum);
    }
}