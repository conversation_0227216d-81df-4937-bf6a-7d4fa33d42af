﻿using System;
using System.Collections.Generic;
using LitJson;
[Serializable]
public class InfoSetting : ConfigInfoBase
{
    public string id;
    public int paramInt;
    public string paramString;
    public override object GetKey(int index)
    {
        return id;
    }

    public override void Parse(JsonData json)
    {
        id = JsonUtil.ToString(json, "id");
        paramInt = JsonUtil.ToInt(json, "paramInt");
        paramString = JsonUtil.ToString(json, "paramString");

        // 检查并替换中文逗号为英文逗号
        if (!string.IsNullOrEmpty(paramString))
        {
            paramString = paramString.Replace('，', ',');
        }
    }
}